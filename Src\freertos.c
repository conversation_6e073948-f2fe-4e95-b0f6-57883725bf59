/* USER CODE BEGIN Header */
/**
  * File Name          : freertos.c
  * Description        : Code for freertos applications
  */
/* USER CODE END Header */

/* Includes ------------------------------------------------------------------*/
#include "FreeRTOS.h"
#include "task.h"
#include "main.h"
#include "cmsis_os.h"

/* Private includes ----------------------------------------------------------*/
/* USER CODE BEGIN Includes */
#include <stdio.h>
#include <string.h>
#include "gpio.h"
#include "rtc.h"
#include "adc.h"
#include "usart.h"
#include "dma.h"
#include "spi.h"
#include "i2c.h"
#include "stm32l0xx_hal_pwr.h"
#include "SPI_FLASH/bsp_spi_flash.h"
#include "lsm6ds3.h"
#include "GPS.h"
#include "rtc_sync.h"
#include "system_modules.h"

// External variables
extern UART_HandleTypeDef huart1;
extern uint8_t uart1_rx_buffer[];
extern char gps_buffer[];
extern uint16_t gps_buffer_index;
#define GPS_BUFFER_SIZE 256

// GM20相关变量已移除

extern GPS_Data_t gps_data;
extern uint8_t gps_new_data;
extern uint8_t gps_data_ready;
extern uint32_t ADC_Value[];  // 更新为uint32_t类型
extern float pw;

// Function declarations
extern HAL_StatusTypeDef Create_Data_String(char *output_buffer, uint16_t buffer_size);
extern void Print_Data_String(const char *data_string);  // 替代GM20发送
extern void GPS_ParseData(void);

// STM32内部温度传感器计算函数
float Calculate_MCU_Temperature(uint32_t temp_adc_value);

// LED强制关闭函数
void Force_LED_Off(void);

// Modular function declarations
// Power management module
HAL_StatusTypeDef PowerModule_Init(void);
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void);
void PowerModule_EnablePeripherals(void);
void PowerModule_DisablePeripherals(void);

// GPS module
HAL_StatusTypeDef GPSModule_Init(void);
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t timeout_ms, uint8_t is_first_boot);
void GPSModule_PowerOn(void);
void GPSModule_PowerOff(void);
HAL_StatusTypeDef GPSModule_SyncRTC(void);

// Sensor module
HAL_StatusTypeDef SensorModule_Init(void);
HAL_StatusTypeDef SensorModule_ReadData(void);

// Data processing module (GM20相关函数已移除)
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size);
void DataModule_PrintData(const char *data_string);

// System control module
void SystemModule_PrintCurrentTime(void);
void SystemModule_EnterSleepMode(void);

// Battery protection module
uint8_t BatteryProtection_CheckVoltage(void);
void BatteryProtection_EnterLowVoltageMode(void);
void BatteryProtection_ExitLowVoltageMode(void);

// 数据缓存管理函数
void DataCache_Init(void);
HAL_StatusTypeDef DataCache_AddData(const char *data_string);
HAL_StatusTypeDef DataCache_GetData(char *data_string);
uint16_t DataCache_GetCount(void);
void DataCache_Clear(void);
uint8_t DataCache_IsFull(void);

// 时间管理函数
uint32_t GetCurrentWakeupTime(void);
// GM20相关时间管理函数已移除

/* USER CODE END Includes */

/* Private typedef -----------------------------------------------------------*/
/* USER CODE BEGIN PTD */

/* USER CODE END PTD */

/* Private define ------------------------------------------------------------*/
/* USER CODE BEGIN PD */

// ADC power control through HAL library functions

// GM20模块配置已移除

// Sleep configuration
#define SLEEP_DURATION_SECONDS 20      // 休眠时间设置
#define BLINK_COUNT     3               // LED blink count
#define BLINK_DELAY     100             // LED blink delay in ms
#define UART1_RX_BUFFER_SIZE 256        // UART receive buffer size

// Battery voltage protection thresholds
#define BATTERY_LOW_VOLTAGE_THRESHOLD    3.4f    // 低电压保护阈值（V）
#define BATTERY_RECOVERY_VOLTAGE_THRESHOLD 3.6f  // 电压恢复阈值（V）

// Voltage monitoring test configuration
#define ENABLE_VOLTAGE_MONITOR_TEST 0   // 1=启用电压监测测试, 0=禁用
#define VOLTAGE_MONITOR_DURATION 60     // 电压监测持续时间（秒）

#define GPS_TIMEOUT_NORMAL 120          // GPS常规定位等待时间
#define GPS_TIMEOUT_FIRST_BOOT 600      // GPS初次启动等待时间（增加到10分钟）
#define GPS_TIMEOUT_WINDOW_TEST 900     // 窗边测试等待时间（15分钟）

// 数据缓存配置
#define DATA_CACHE_SIZE 20              // 数据缓存条数
#define DATA_CACHE_ITEM_SIZE 120        // 每条数据占用的字节数

/* USER CODE END PD */

/* Private macro -------------------------------------------------------------*/
/* USER CODE BEGIN PM */

/* USER CODE END PM */

/* Private variables ---------------------------------------------------------*/
/* USER CODE BEGIN Variables */
volatile uint8_t rtcWakeupFlag = 0;  // RTC wakeup flag
extern uint8_t uart1_rx_buffer[UART1_RX_BUFFER_SIZE]; // UART receive buffer

// LSM6DS3 sensor data structures - defined in main.c
extern LSM6DS3_Data imuData;        // IMU sensor data
extern LSM6DS3_Attitude attitude;   // Attitude data from sensor fusion - defined in main.c

// Dynamic sleep time control variables
uint32_t wakeup_counter = 0;        // Dynamic wakeup counter, 0 means use default value

// Global first boot flag
uint8_t is_first_boot = 1;          // First boot flag for forced RTC sync

// Battery voltage protection status flag
uint8_t low_voltage_protection_active = 0;  // Low voltage protection status: 0=normal mode, 1=protection mode

// GM20启动计数器已移除

// RTOS communication objects
// Queue handles
osMessageQId gpsDataQueueHandle;
osMessageQId sensorDataQueueHandle;
// GM20命令队列已移除

// Semaphore handles
osSemaphoreId gpsReadySemHandle;
osSemaphoreId sensorReadySemHandle;
osSemaphoreId dataSentSemHandle;

// Event group handles - using semaphores to simulate event group functionality
osSemaphoreId gpsStartSemHandle;
osSemaphoreId sensorStartSemHandle;

// Event group flag definitions
#define GPS_READY_BIT       (1UL << 0)
#define SENSOR_READY_BIT    (1UL << 1)
#define DATA_SENT_BIT       (1UL << 2)
#define SLEEP_READY_BIT     (1UL << 3)

// Data structure definitions are in system_modules.h
/* USER CODE END Variables */
osThreadId GPSTaskHandle;
uint32_t GPSTaskBuffer[ 512 ];
osStaticThreadDef_t GPSTaskControlBlock;
osThreadId AccelTaskHandle;
uint32_t AccelTaskBuffer[ 512 ];
osStaticThreadDef_t AccelTaskControlBlock;
// GM20任务已移除
osThreadId FlashTaskHandle;
uint32_t FlashTaskBuffer[ 128 ];
osStaticThreadDef_t FlashTaskControlBlock;
osThreadId myPowerTaskHandle;
uint32_t myPowerTaskBuffer[ 512 ];
osStaticThreadDef_t myPowerTaskControlBlock;

// 数据缓存结构体
typedef struct {
    char data[DATA_CACHE_ITEM_SIZE];    // 数据内容
    uint32_t timestamp;                 // 时间戳
    uint8_t valid;                      // 数据有效性标志
} DataCacheItem_t;

// 数据缓存管理结构体
typedef struct {
    DataCacheItem_t items[DATA_CACHE_SIZE];  // 数据项数组
    uint16_t write_index;                    // 写入索引
    uint16_t read_index;                     // 读取索引
    uint16_t count;                          // 当前缓存中的数据条数
    uint8_t is_full;                         // 缓存是否已满
} DataCache_t;

// GM20工作状态结构体已移除

// 数据缓存
static DataCache_t data_cache;          // 数据缓存管理结构
static uint8_t data_cache_buffer[DATA_CACHE_SIZE * DATA_CACHE_ITEM_SIZE];  // 实际缓存空间

// GM20工作状态管理已移除
static uint32_t device_wakeup_count = 0;  // 设备唤醒次数

/* Private function prototypes -----------------------------------------------*/
/* USER CODE BEGIN FunctionPrototypes */
void EnterStopMode(void);
void BlinkLEDs(uint8_t count, uint32_t delay);
void SystemClock_Config(void);  // System clock config function defined in main.c
/* USER CODE END FunctionPrototypes */

void StartGPSTask(void const * argument);
void StartAccelTask(void const * argument);
// GM20任务函数声明已移除
void StartFlashTask(void const * argument);
void StartPowerTask(void const * argument);

void MX_FREERTOS_Init(void); /* (MISRA C 2004 rule 8.1) */

/* GetIdleTaskMemory prototype (linked to static allocation support) */
void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize );

/* USER CODE BEGIN GET_IDLE_TASK_MEMORY */
static StaticTask_t xIdleTaskTCBBuffer;
static StackType_t xIdleStack[configMINIMAL_STACK_SIZE];

void vApplicationGetIdleTaskMemory( StaticTask_t **ppxIdleTaskTCBBuffer, StackType_t **ppxIdleTaskStackBuffer, uint32_t *pulIdleTaskStackSize )
{
  *ppxIdleTaskTCBBuffer = &xIdleTaskTCBBuffer;
  *ppxIdleTaskStackBuffer = &xIdleStack[0];
  *pulIdleTaskStackSize = configMINIMAL_STACK_SIZE;
  /* place for user code */
}
/* USER CODE END GET_IDLE_TASK_MEMORY */

/**
  * @brief  FreeRTOS initialization
  * @param  None
  * @retval None
  */
void MX_FREERTOS_Init(void) {
  /* USER CODE BEGIN Init */

  /* USER CODE END Init */

  /* USER CODE BEGIN RTOS_MUTEX */
  /* add mutexes, ... */
  /* USER CODE END RTOS_MUTEX */

  /* USER CODE BEGIN RTOS_SEMAPHORES */
  // Create data ready semaphores
  osSemaphoreDef(gpsReadySem);
  gpsReadySemHandle = osSemaphoreCreate(osSemaphore(gpsReadySem), 1);

  osSemaphoreDef(sensorReadySem);
  sensorReadySemHandle = osSemaphoreCreate(osSemaphore(sensorReadySem), 1);

  osSemaphoreDef(dataSentSem);
  dataSentSemHandle = osSemaphoreCreate(osSemaphore(dataSentSem), 1);

  // Create task start semaphores
  osSemaphoreDef(gpsStartSem);
  gpsStartSemHandle = osSemaphoreCreate(osSemaphore(gpsStartSem), 1);

  osSemaphoreDef(sensorStartSem);
  sensorStartSemHandle = osSemaphoreCreate(osSemaphore(sensorStartSem), 1);

  // Initial state: acquire data ready semaphores, wait for task release
  osSemaphoreWait(gpsReadySemHandle, 0);
  osSemaphoreWait(sensorReadySemHandle, 0);
  osSemaphoreWait(dataSentSemHandle, 0);

  // Initial state: acquire start semaphores, wait for main task release
  osSemaphoreWait(gpsStartSemHandle, 0);
  osSemaphoreWait(sensorStartSemHandle, 0);
  /* USER CODE END RTOS_SEMAPHORES */

  /* USER CODE BEGIN RTOS_TIMERS */
  /* start timers, add new ones, ... */
  /* USER CODE END RTOS_TIMERS */

  /* USER CODE BEGIN RTOS_QUEUES */
  // Create message queues
  osMessageQDef(gpsDataQueue, 2, GPSQueueData_t);
  gpsDataQueueHandle = osMessageCreate(osMessageQ(gpsDataQueue), NULL);

  osMessageQDef(sensorDataQueue, 2, SensorQueueData_t);
  sensorDataQueueHandle = osMessageCreate(osMessageQ(sensorDataQueue), NULL);

  // GM20命令队列已移除
  /* USER CODE END RTOS_QUEUES */

  /* Create the thread(s) */
  /* definition and creation of GPSTask */
  osThreadStaticDef(GPSTask, StartGPSTask, osPriorityNormal, 0, 512, GPSTaskBuffer, &GPSTaskControlBlock);
  GPSTaskHandle = osThreadCreate(osThread(GPSTask), NULL);

  /* definition and creation of AccelTask */
  osThreadStaticDef(AccelTask, StartAccelTask, osPriorityIdle, 0, 512, AccelTaskBuffer, &AccelTaskControlBlock);
  AccelTaskHandle = osThreadCreate(osThread(AccelTask), NULL);

  // GM20任务创建已移除

  /* definition and creation of FlashTask */
  osThreadStaticDef(FlashTask, StartFlashTask, osPriorityIdle, 0, 128, FlashTaskBuffer, &FlashTaskControlBlock);
  FlashTaskHandle = osThreadCreate(osThread(FlashTask), NULL);

  /* definition and creation of myPowerTask */
  osThreadStaticDef(myPowerTask, StartPowerTask, osPriorityIdle, 0, 512, myPowerTaskBuffer, &myPowerTaskControlBlock);
  myPowerTaskHandle = osThreadCreate(osThread(myPowerTask), NULL);

  /* USER CODE BEGIN RTOS_THREADS */
  printf("RTOS communication objects created successfully\r\n");
  /* USER CODE END RTOS_THREADS */

}

/* USER CODE BEGIN Header_StartGPSTask */
// GPS task implementation
/* USER CODE END Header_StartGPSTask */
void StartGPSTask(void const * argument)
{
  /* USER CODE BEGIN StartGPSTask */



  /* Infinite loop */
  for(;;)
  {
    // Wait for main task start signal
    if (osSemaphoreWait(gpsStartSemHandle, osWaitForever) == osOK) {
      // Execute GPS data acquisition
      GPSModule_PowerOn();
      // 修改：使用宏定义，让函数内部根据is_first_boot参数决定等待时间
      HAL_StatusTypeDef gps_result = GPSModule_WaitForData(0, is_first_boot);

      // GPS clock sync logic optimization (before turning off GPS power)
      if (gps_result == HAL_OK) {
        // Wait for GPS data to stabilize completely
        HAL_Delay(1000);

        if (is_first_boot) {
          // First boot forced RTC sync
          GPSModule_SyncRTC();  // 不管同步结果如何
          is_first_boot = 0;    // 第一次GPS尝试后就清零标志
        } else {
          // Normal working cycle conditional sync
          GPSModule_SyncRTC();
        }
      } else {
        // GPS等待超时，也清零first boot标志
        if (is_first_boot) {
          is_first_boot = 0;
          printf("First GPS attempt timeout, switching to normal timeout\r\n");
        }
      }

      // Turn off GPS power after RTC sync to save power
      GPSModule_PowerOff();

      // Release GPS data ready semaphore
      osSemaphoreRelease(gpsReadySemHandle);
    }

    // Brief delay to avoid excessive CPU usage
    osDelay(10);
  }
  /* USER CODE END StartGPSTask */
}

/* USER CODE BEGIN Header_StartAccelTask */
// Accelerometer/sensor task implementation
/* USER CODE END Header_StartAccelTask */
void StartAccelTask(void const * argument)
{
  /* USER CODE BEGIN StartAccelTask */



  /* Infinite loop */
  for(;;)
  {
    // Wait for main task start signal
    if (osSemaphoreWait(sensorStartSemHandle, osWaitForever) == osOK) {
      // Execute sensor data reading
      SensorModule_Init();
      SensorModule_ReadData();

      // Release sensor data ready semaphore
      osSemaphoreRelease(sensorReadySemHandle);
    }

    // Brief delay to avoid excessive CPU usage
    osDelay(10);
  }
  /* USER CODE END StartAccelTask */
}

// GM20任务实现已移除

/* USER CODE BEGIN Header_StartFlashTask */
// Flash task implementation
/* USER CODE END Header_StartFlashTask */
void StartFlashTask(void const * argument)
{
  /* USER CODE BEGIN StartFlashTask */
  /* Infinite loop */
  for(;;)
  {
    osDelay(1);
  }
  /* USER CODE END StartFlashTask */
}

/* USER CODE BEGIN Header_StartPowerTask */
// Main power control task implementation
/* USER CODE END Header_StartPowerTask */
void StartPowerTask(void const * argument)
{
  /* USER CODE BEGIN StartPowerTask */
  uint32_t cycle_count = 0;

//  printf("Main Control Task started\r\n");

  /* Infinite loop */
  for(;;)
  {
    cycle_count++;

    printf("===== Cycle #%lu (Wakeup #%lu, Time: %lu min) =====\r\n",
           cycle_count, device_wakeup_count, GetCurrentWakeupTime());

    // Reset semaphore states for each cycle to ensure proper synchronization
    // Clear any pending start semaphores (in case they were left in released state)
    osSemaphoreWait(gpsStartSemHandle, 0);
    osSemaphoreWait(sensorStartSemHandle, 0);

    // Clear any pending ready semaphores (in case they were left in released state)
    osSemaphoreWait(gpsReadySemHandle, 0);
    osSemaphoreWait(sensorReadySemHandle, 0);

    // 1. Print RTC time immediately after wakeup
    SystemModule_PrintCurrentTime();

    // 2. Power management module initialization
    PowerModule_Init();

    // 3. Check battery voltage
    PowerModule_ReadBatteryVoltage();

    // 4. Battery voltage protection check
    uint8_t voltage_protection_result = BatteryProtection_CheckVoltage();
    if (voltage_protection_result == 1) {
        // Enter low voltage protection mode, skip all data collection work
        printf("Low voltage protection active - skipping data collection\r\n");
        SystemModule_EnterSleepMode();
        continue; // Skip the rest of this loop
    } else if (voltage_protection_result == 2) {
        // Voltage recovered, exit low voltage protection mode
        printf("Voltage recovered - resuming normal operation\r\n");
    }

    // 5. 增加设备唤醒计数
    device_wakeup_count++;

    // 6. Start GPS and sensor tasks (parallel execution)
    osSemaphoreRelease(gpsStartSemHandle);
    osSemaphoreRelease(sensorStartSemHandle);

    // 7. Wait for GPS and sensor data ready
    uint8_t gps_ok = 0, sensor_ok = 0;
    // GPS等待超时时间根据is_first_boot参数动态设置
    uint32_t gps_wait_timeout_ms = is_first_boot ? (GPS_TIMEOUT_FIRST_BOOT * 1000 + 10000) : (GPS_TIMEOUT_NORMAL * 1000 + 10000);
    printf("GPS task timeout: %lu ms (%s boot)\r\n", gps_wait_timeout_ms, is_first_boot ? "first" : "normal");
    if (osSemaphoreWait(gpsReadySemHandle, gps_wait_timeout_ms) == osOK) {
      gps_ok = 1;
    }
    if (osSemaphoreWait(sensorReadySemHandle, 5000) == osOK) {
      sensor_ok = 1;
    }

    // Print data collection results
    if (gps_ok && gps_data.valid) {
      printf("GPS: FIXED - %d sats, HDOP=%.1f, Lat=%.5f, Lon=%.5f\r\n",
             gps_data.satellites, gps_data.hdop, gps_data.latitude, gps_data.longitude);
    } else if (gps_ok) {
      printf("GPS: SEARCHING - %d sats visible, HDOP=%.1f (no fix yet)\r\n",
             gps_data.satellites, gps_data.hdop);
    } else {
      printf("GPS: No signal\r\n");
    }

    if (sensor_ok) {
      printf("Sensor: %.1fC, Roll=%.1f, Pitch=%.1f, Yaw=%.1f\r\n",
             imuData.temp_celsius, attitude.roll, attitude.pitch, attitude.yaw);
    }

    // 8. 数据合成和打印输出
    if (gps_ok && gps_data.valid) {
      char data_string[300];
      if (DataModule_CreatePacket(data_string, sizeof(data_string)) == HAL_OK) {
        DataModule_PrintData(data_string);
      }
    } else {
      printf("GPS data invalid, skip data synthesis\r\n");
    }

    // 9. Enter sleep mode
    SystemModule_EnterSleepMode();

    printf("Cycle #%lu completed\r\n\n", cycle_count);

    // 强制关闭LED，确保每个循环结束时LED都关闭
    Force_LED_Off();

    // Note: is_first_boot flag is cleared by GPS task after successful RTC sync
    osDelay(500);
  }
  /* USER CODE END StartPowerTask */
}

/* Private application code --------------------------------------------------*/
/* USER CODE BEGIN Application */

// Blink LEDs alternately - 修改：移除LED闪烁功能，LED只用于GPS等待提示
void BlinkLEDs(uint8_t count, uint32_t delay)
{
  // 修改：LED只用于GPS等待提示，移除其他LED闪烁功能
  // 直接关闭LED，确保LED不会常亮
  Force_LED_Off();
}

// Enter STOP mode - ultra simplified version
void EnterStopMode(void)
{
  // 强制关闭LED，确保进入休眠模式前LED一定关闭
  Force_LED_Off();

  // Turn off all peripheral power to save energy
  GPS_PWR_OFF;  // Turn off GPS power using correct macro
  V_OUT_OFF;    // Turn off sensor power using macro
  ADC_OFF;      // Turn off ADC power using macro
  RF_PWR_OFF;   // Turn off RF module power to save power (GM20相关代码已移除)

  // Stop UART receive interrupts
  HAL_UART_AbortReceive_IT(&huart1);
  // LPUART1相关代码已移除（GM20）

  // Disable UART interrupts
  HAL_NVIC_DisableIRQ(USART1_IRQn);
  HAL_NVIC_DisableIRQ(LPUART1_IRQn);

  // Turn off all peripheral clocks
  __HAL_RCC_I2C1_CLK_DISABLE();
  __HAL_RCC_USART1_CLK_DISABLE();
  __HAL_RCC_LPUART1_CLK_DISABLE();

  // Ensure UART transmission is complete
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);

  // Configure RTC wakeup timer

  // Ensure all interrupts are processed
  __disable_irq();

  // Deactivate any existing wakeup timer
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

  // Clear any pending flags
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(&hrtc, RTC_FLAG_WUTF);
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // Set wakeup timer with RTC_WAKEUPCLOCK_CK_SPRE_16BITS (1Hz clock source)
  // Use external variable or default value
  // Sleep time = Counter / 1Hz, so Counter = sleep seconds - 1
  uint32_t default_wakeup_counter = SLEEP_DURATION_SECONDS - 1;
  uint32_t actual_wakeup_counter = (wakeup_counter > 0) ? wakeup_counter : default_wakeup_counter;

  // Setting RTC wakeup counter

  if (HAL_RTCEx_SetWakeUpTimer_IT(&hrtc, actual_wakeup_counter, RTC_WAKEUPCLOCK_CK_SPRE_16BITS) != HAL_OK) {
    printf("Failed to set wakeup timer\r\n");
    __enable_irq();
    return;
  }

  // Enable RTC interrupt with highest priority
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // Enable EXTI for RTC wakeup
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_IT();
  __HAL_RTC_WAKEUPTIMER_EXTI_ENABLE_RISING_EDGE();

  __enable_irq();

  // Get current time
  RTC_TimeTypeDef time_before;
  RTC_DateTypeDef date_before;
  HAL_RTC_GetTime(&hrtc, &time_before, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_before, RTC_FORMAT_BIN);

  printf("Time before sleep: %02d:%02d:%02d\r\n",
         time_before.Hours, time_before.Minutes, time_before.Seconds);

  // Make sure the message is sent
  while(__HAL_UART_GET_FLAG(&huart1, UART_FLAG_TC) == RESET);
  HAL_Delay(10);

  // Disable all interrupts, prepare to enter low power mode
  __disable_irq();

  // Disable SysTick interrupt
  SysTick->CTRL &= ~SysTick_CTRL_TICKINT_Msk;

  // Disable all interrupts, keep only RTC wakeup interrupt
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICER[i] = 0xFFFFFFFF;
  }

  // Ensure RTC wakeup interrupt is enabled
  HAL_NVIC_SetPriority(RTC_IRQn, 0, 0);
  HAL_NVIC_EnableIRQ(RTC_IRQn);

  // Clear all pending interrupts
  for (uint8_t i = 0; i < 8; i++) {
    NVIC->ICPR[i] = 0xFFFFFFFF;
  }

  // Suspend scheduler
  vTaskSuspendAll();

  // Re-enable global interrupts, but only RTC interrupt is active
  __enable_irq();

  // Enter STOP mode with low power regulator
  HAL_PWR_EnterSTOPMode(PWR_LOWPOWERREGULATOR_ON, PWR_STOPENTRY_WFI);

  // After wakeup - code below executes after waking up

  // 修改：移除唤醒LED指示，LED只用于GPS等待提示
  // Turn on LED to indicate wakeup started
  // LED1_ON;  // 注释掉唤醒LED指示

  // Reconfigure system clock - required after STOP mode
  SystemClock_Config();

  // Re-initialize all necessary peripheral clocks
  __HAL_RCC_USART1_CLK_ENABLE();
  __HAL_RCC_LPUART1_CLK_ENABLE();
  __HAL_RCC_I2C1_CLK_ENABLE();
  __HAL_RCC_DMA1_CLK_ENABLE();

  // Re-initialize UART1 and LPUART1
  MX_USART1_UART_Init();
  MX_LPUART1_UART_Init();

  // Re-enable UART interrupts
  HAL_NVIC_SetPriority(USART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(USART1_IRQn);
  HAL_NVIC_SetPriority(LPUART1_IRQn, 3, 0);
  HAL_NVIC_EnableIRQ(LPUART1_IRQn);

  // Restart UART receive interrupts
  HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);

  // Re-enable SysTick interrupt
  SysTick->CTRL |= SysTick_CTRL_TICKINT_Msk;

  // Re-enable global interrupts
  __enable_irq();

  // 修改：移除唤醒LED闪烁指示，LED只用于GPS等待提示
  // Blink LED to indicate wakeup in progress
  // LED1_OFF;
  // HAL_Delay(100);
  // LED1_ON;  // 注释掉唤醒LED指示

  // Get current time
  RTC_TimeTypeDef time_after;
  RTC_DateTypeDef date_after;
  HAL_RTC_GetTime(&hrtc, &time_after, RTC_FORMAT_BIN);
  HAL_RTC_GetDate(&hrtc, &date_after, RTC_FORMAT_BIN);

  // Resume scheduler
  xTaskResumeAll();

  // Stop wakeup timer
  HAL_RTCEx_DeactivateWakeUpTimer(&hrtc);

//  printf("Woke up from sleep mode\r\n");
  printf("Time after sleep: %02d:%02d:%02d\r\n",
         time_after.Hours, time_after.Minutes, time_after.Seconds);

  // Calculate elapsed time
  int elapsed_seconds = (time_after.Hours - time_before.Hours) * 3600 +
                        (time_after.Minutes - time_before.Minutes) * 60 +
                        (time_after.Seconds - time_before.Seconds);
  if (elapsed_seconds < 0) {
    elapsed_seconds += 24 * 3600; // Handle day rollover
  }

  printf("Elapsed time: %d seconds\r\n", elapsed_seconds);

  // 修改：确保LED关闭，LED只用于GPS等待提示
  Force_LED_Off();

  // Re-enable peripherals
  PowerModule_EnablePeripherals();
}

// RTC wakeup timer event callback
void HAL_RTCEx_WakeUpTimerEventCallback(RTC_HandleTypeDef *hrtc)
{
  // Set wakeup flag
  rtcWakeupFlag = 1;

  // Clear RTC wakeup flag
  __HAL_RTC_WAKEUPTIMER_CLEAR_FLAG(hrtc, RTC_FLAG_WUTF);

  // Clear EXTI line flag
  __HAL_RTC_WAKEUPTIMER_EXTI_CLEAR_FLAG();

  // Note: Do not use printf here as it may crash the system when called from interrupt context
}



// Modular function implementations

// Power management module initialization
HAL_StatusTypeDef PowerModule_Init(void)
{
    // Turn on power switches
    ADC_ON;   // Turn on ADC voltage measurement switch
    V_OUT_ON; // Turn on 3-axis and SPI_FLASH power
    HAL_Delay(2000); // Wait for stabilization

    return HAL_OK;
}

// Read battery voltage using pure VREFINT calibration (consistent with voltage test)
HAL_StatusTypeDef PowerModule_ReadBatteryVoltage(void)
{
    extern ADC_HandleTypeDef hadc;
    extern uint32_t ADC_Value[60];  // 3通道×20采样=60个数据

    // Clear ADC data array before starting
    memset(ADC_Value, 0, sizeof(ADC_Value));

    // Turn on ADC power
    ADC_ON;  // Use macro to turn on ADC power
    HAL_Delay(10);

    float voltage = 0.0f;

    // Force ADC reset and re-initialization
    HAL_ADC_DeInit(&hadc);
    HAL_Delay(10);
    MX_ADC_Init();
    HAL_Delay(10);

    // Calibrate ADC
    HAL_ADCEx_Calibration_Start(&hadc, ADC_SINGLE_ENDED);

    if (HAL_ADC_Start_DMA(&hadc, (uint32_t*)ADC_Value, 60) == HAL_OK) {
        HAL_Delay(100);

        // ADC data collection completed

        // Separate data from three channels
        // Correct channel order confirmed by actual testing: CH6(voltage) -> VREFINT(reference voltage) -> TEMP(temperature)
        uint32_t battery_adc_sum = 0;
        uint32_t temp_adc_sum = 0;
        uint32_t vrefint_adc_sum = 0;
        uint32_t battery_count = 0;
        uint32_t temp_count = 0;
        uint32_t vrefint_count = 0;

        for (int i = 0; i < 60; i += 3) {
            // CH6 (battery voltage) - index 0, 3, 6, 9... (2224)
            battery_adc_sum += ADC_Value[i];
            battery_count++;

            // VREFINT (internal reference voltage) - index 1, 4, 7, 10... (1526)
            if (i + 1 < 60) {
                vrefint_adc_sum += ADC_Value[i + 1];
                vrefint_count++;
            }

            // TEMPSENSOR (internal temperature) - index 2, 5, 8, 11... (622)
            if (i + 2 < 60) {
                temp_adc_sum += ADC_Value[i + 2];
                temp_count++;
            }
        }

        float adc_raw = (battery_count > 0) ? (battery_adc_sum / (float)battery_count) : 0;
        float vrefint_raw = (vrefint_count > 0) ? (vrefint_adc_sum / (float)vrefint_count) : 0;
        float temp_raw = (temp_count > 0) ? (temp_adc_sum / (float)temp_count) : 0;

        // ADC average values calculated

        HAL_ADC_Stop_DMA(&hadc);

        // Calculate and update MCU internal temperature with VDD correction
        if (temp_raw > 0 && vrefint_raw > 0) {
            extern float mcu_temp;

            // Apply VDD correction to temperature reading (same as voltage monitoring test)
            #define VREFINT_CAL_ADDR    ((uint16_t*) 0x1FF80078)
            #define VREFINT_CAL_VREF    3000  // mV

            uint16_t vrefint_cal = *VREFINT_CAL_ADDR;
            float actual_vdd_temp = (float)(VREFINT_CAL_VREF * vrefint_cal) / vrefint_raw / 1000.0f;
            float temp_corrected = temp_raw * actual_vdd_temp / 3.0f;

            mcu_temp = Calculate_MCU_Temperature((uint32_t)temp_corrected);
        }

        // Calculate voltage using VREFINT calibration (same as original 2-channel version)
        if (vrefint_raw > 0) {
            #define VREFINT_CAL_ADDR_POWER    ((uint16_t*) 0x1FF80078)
            #define VREFINT_CAL_VREF_POWER    3000

            uint16_t vrefint_cal = *VREFINT_CAL_ADDR_POWER;
            float actual_vdd = (float)(VREFINT_CAL_VREF_POWER * vrefint_cal) / vrefint_raw / 1000.0f;

            // Calculate base voltage (x2 for hardware voltage divider)
            voltage = (adc_raw * actual_vdd / 4096.0f) * 2.0f;

        } else {
            // Use fixed VDD when VREFINT fails (x2 for hardware voltage divider)
            voltage = (adc_raw * 3.3f / 4096.0f) * 2.0f;
        }

        // Apply calibration factor and offset to compensate for hardware errors
        voltage = voltage * VOLTAGE_CALIBRATION_FACTOR + VOLTAGE_OFFSET;

        // Voltage calculation completed
    } else {
        printf("ADC DMA start failed!\r\n");
    }

    // Turn off ADC power
    ADC_OFF;  // Use macro to turn off ADC power

    pw = voltage;
    printf("Battery: %.3f V\r\n", pw);

    return HAL_OK;
}

// Enable peripheral power
void PowerModule_EnablePeripherals(void)
{
    // 重新初始化电源控制引脚为输出模式（设备唤醒后）
    GPIO_ReInit_PowerPins();

    // Turn on power for sensors only (GPS power controlled separately)
    V_OUT_ON;

    // GPS power will be controlled by GPSModule_PowerOn() when needed
    printf("Peripheral power enabled, GPS power will be controlled separately\r\n");

    // Clear GPS buffer
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);
    gps_buffer_index = 0;

    // Ensure UART receive interrupt is started
    if (huart1.RxState != HAL_UART_STATE_BUSY_RX) {
        // If UART receive is not started, restart it
        HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
    }

    // 修改：移除LED指示，LED只用于GPS等待提示
    // Blink LED to indicate successful wakeup
    // LED1_ON;
    // HAL_Delay(50);  // Reduced delay: 100ms -> 50ms
    // LED1_OFF;  // 注释掉LED指示

    // Ensure I2C is initialized
    if (hi2c1.State == HAL_I2C_STATE_RESET) {
        MX_I2C1_Init();
    }
}

// Disable peripheral power
void PowerModule_DisablePeripherals(void)
{
    ADC_OFF;
    V_OUT_OFF;
    GPS_PWR_OFF;
    RF_PWR_OFF; // Turn off RF module power to save power (GM20相关代码已移除)
}

// GPS module initialization
HAL_StatusTypeDef GPSModule_Init(void)
{
    return HAL_OK;
}

// GPS module power on
void GPSModule_PowerOn(void)
{
    printf("Turning on GPS power...\r\n");

    // Force re-initialize GPS power pin to ensure correct configuration
    GPIO_InitTypeDef GPIO_InitStruct = {0};
    GPIO_InitStruct.Pin = GPS_PWR_Pin;
    GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
    GPIO_InitStruct.Pull = GPIO_NOPULL;
    GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
    HAL_GPIO_Init(GPIOB, &GPIO_InitStruct);
    printf("GPS power pin re-initialized\r\n");

    // Check GPS power pin state before turning on
    GPIO_PinState pin_state_before = HAL_GPIO_ReadPin(GPIOB, GPS_PWR_Pin);
    printf("GPS_PWR_Pin state before: %d\r\n", pin_state_before);

    // Force GPS power off first, then on (ensure clean power cycle)
    printf("Performing GPS power cycle...\r\n");
    HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_SET);    // OFF
    HAL_Delay(200);
    HAL_GPIO_WritePin(GPIOB, GPS_PWR_Pin, GPIO_PIN_RESET);  // ON
    HAL_Delay(100);

    // Check GPS power pin state after turning on
    GPIO_PinState pin_state_after = HAL_GPIO_ReadPin(GPIOB, GPS_PWR_Pin);
    printf("GPS_PWR_Pin state after: %d\r\n", pin_state_after);

    // Verify the pin state is correct (should be 0 for GPS_PWR_ON)
    if (pin_state_after != GPIO_PIN_RESET) {
        printf("ERROR: GPS power pin still not correct! Hardware issue?\r\n");
        // Try one more time with direct register access
        GPIOB->BSRR = GPS_PWR_Pin << 16;  // Reset pin (turn on GPS)
        HAL_Delay(100);
        GPIO_PinState final_state = HAL_GPIO_ReadPin(GPIOB, GPS_PWR_Pin);
        printf("GPS_PWR_Pin final state (direct): %d\r\n", final_state);
    } else {
        printf("GPS power control successful\r\n");
    }

    HAL_Delay(400);  // Additional delay for GPS module startup

    // Check UART configuration
    printf("UART1 Config: Baud=%lu, WordLength=%lu, StopBits=%lu, Parity=%lu\r\n",
           huart1.Init.BaudRate, huart1.Init.WordLength, huart1.Init.StopBits, huart1.Init.Parity);

    // Restart UART receive interrupt to ensure it's working
    HAL_StatusTypeDef uart_abort_status = HAL_UART_AbortReceive_IT(&huart1);
    printf("UART abort status: %d\r\n", uart_abort_status);

    HAL_Delay(10);

    HAL_StatusTypeDef uart_start_status = HAL_UART_Receive_IT(&huart1, &uart1_rx_buffer[0], 1);
    printf("UART start receive status: %d\r\n", uart_start_status);

    printf("GPS power on, UART restarted, waiting for module startup...\r\n");

    // Test UART transmission to GPS (some GPS modules respond to commands)
    HAL_Delay(1000);  // Wait for GPS module to fully start
    printf("Sending test command to GPS...\r\n");
    char test_cmd[] = "$PMTK000*32\r\n";  // Query release information
    HAL_StatusTypeDef tx_status = HAL_UART_Transmit(&huart1, (uint8_t*)test_cmd, strlen(test_cmd), 1000);
    printf("GPS command send status: %d\r\n", tx_status);
}

// GPS module power off
void GPSModule_PowerOff(void)
{
    GPS_PWR_OFF;
}

// Wait for GPS data
HAL_StatusTypeDef GPSModule_WaitForData(uint32_t timeout_ms, uint8_t is_first_boot)
{
    uint32_t gps_wait_start;
    uint8_t gps_data_valid = 0;

    // 新增：临时保存估算定位数据
    uint8_t has_estimated_fix = 0;
    GPS_Data_t estimated_data = {0};  // 临时保存估算数据

    // 现有的重置逻辑保持不变
    gps_data.valid = 0;
    gps_data_ready = 0;
    gps_new_data = 0;
    gps_buffer_index = 0;
    memset(gps_buffer, 0, GPS_BUFFER_SIZE);

    // Set GPS wait timeout - 修改：使用宏定义根据is_first_boot参数决定等待时间
    uint32_t gps_wait_timeout;
    if (timeout_ms == 0) {
        // 使用宏定义，根据is_first_boot参数决定等待时间
        gps_wait_timeout = is_first_boot ? GPS_TIMEOUT_FIRST_BOOT * 1000 : GPS_TIMEOUT_NORMAL * 1000;
        printf("GPS wait time: %s boot, timeout: %lu seconds\r\n",
               is_first_boot ? "first" : "normal", gps_wait_timeout / 1000);
    } else {
        // 使用传入的timeout_ms参数（向后兼容）
        gps_wait_timeout = timeout_ms;
        printf("GPS wait time: using parameter, timeout: %lu seconds\r\n", gps_wait_timeout / 1000);
    }

    gps_wait_start = HAL_GetTick();

    // 确保GPS等待开始前LED关闭
    Force_LED_Off();

    // Wait for GPS first fix
    printf("Waiting for GPS data, will print raw NMEA data...\r\n");
    printf("GPS buffer index: %d, UART state: %d\r\n", gps_buffer_index, huart1.RxState);

    uint32_t last_debug_time = HAL_GetTick();
    uint16_t last_buffer_index = gps_buffer_index;
    uint8_t test_sent = 0;

    while ((HAL_GetTick() - gps_wait_start) < gps_wait_timeout) {
        // Send a test command after 3 seconds if no data received
        if (!test_sent && (HAL_GetTick() - gps_wait_start) > 3000 && gps_buffer_index == 0) {
            printf("No GPS data received, sending wake-up command...\r\n");
            char wake_cmd[] = "\r\n";  // Simple wake-up
            HAL_UART_Transmit(&huart1, (uint8_t*)wake_cmd, strlen(wake_cmd), 100);
            test_sent = 1;
        }

        // Print debug info every 10 seconds (reduced frequency)
        if ((HAL_GetTick() - last_debug_time) > 10000) {
            uint32_t elapsed_sec = (HAL_GetTick() - gps_wait_start) / 1000;
            uint32_t remaining_sec = (gps_wait_timeout - (HAL_GetTick() - gps_wait_start)) / 1000;

            printf("GPS search: %lu/%lu sec, sats=%d, HDOP=%.1f, status=%s\r\n",
                   elapsed_sec, gps_wait_timeout/1000,
                   gps_data.satellites, gps_data.hdop,
                   gps_data.valid ? "FIXED" : "SEARCHING");

            last_debug_time = HAL_GetTick();
            last_buffer_index = gps_buffer_index;
        }

        if (gps_new_data) {
            gps_new_data = 0;

            // Print GPS raw data
            printf("GPS Raw: %s\r\n", gps_buffer);

            GPS_ParseData();
        }

        // 检查高精度定位
        if (gps_data.valid && gps_data.fix_quality >= 1 && gps_data.fix_quality <= 5) {
            gps_data_valid = 1;
            // 修复：GPS定位成功时确保LED关闭
            Force_LED_Off();
            break;  // 找到高精度定位，立即退出
        }
        // 检查估算定位
        else if (gps_data.fix_quality == 6 && !has_estimated_fix) {
            has_estimated_fix = 1;
            memcpy(&estimated_data, &gps_data, sizeof(GPS_Data_t));
            estimated_data.valid = 1;  // 标记为有效
        }

        // LED闪烁逻辑保持不变
        LED1_ON;
        osDelay(100);
        LED1_OFF;
        osDelay(100);
    }

    // 修复：确保GPS等待结束后LED关闭，避免LED常亮
    // 无论什么情况下退出while循环，都要确保LED关闭
    Force_LED_Off();

    // 超时后的处理
    if (gps_data_valid) {
        // 使用高精度数据，现有逻辑不变
        return HAL_OK;
    } else if (has_estimated_fix) {
        // 使用估算数据
        memcpy(&gps_data, &estimated_data, sizeof(GPS_Data_t));
        return HAL_OK;
    } else {
        // 使用默认无效数据，现有逻辑不变
        gps_data.latitude = 0.0;
        // ... 其他默认值设置 ...
        return HAL_TIMEOUT;
    }
}

// GPS clock synchronization
HAL_StatusTypeDef GPSModule_SyncRTC(void)
{
    // Check if GPS data is valid
    if (!gps_data.valid) {
        return HAL_ERROR;
    }

    // Get current RTC time
    RTC_TimeTypeDef rtc_time;
    RTC_DateTypeDef rtc_date;
    HAL_RTC_GetTime(&hrtc, &rtc_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &rtc_date, RTC_FORMAT_BIN);

    // Smart GPS time sync logic: when GPS has positioning info, time is definitely valid
    // Check if GPS time data is valid (time is more important than date)
    uint8_t time_valid = (gps_data.hour <= 23 && gps_data.minute <= 59 && gps_data.second <= 59);
    uint8_t date_valid = (gps_data.year >= 2020 && gps_data.month >= 1 &&
                         gps_data.month <= 12 && gps_data.day >= 1 && gps_data.day <= 31);

    // When GPS has positioning info, time is definitely valid, should not reject sync due to time format issues
    // Only skip sync when GPS is completely invalid
    if (!gps_data.valid) {
        return HAL_ERROR;
    }

    // If GPS has positioning but time format is abnormal, use default time handling
    if (!time_valid) {
        printf("GPS time format invalid, using RTC time for sync\r\n");
        return HAL_OK;  // Keep current RTC time
    }

    // Calculate time difference (minutes)
    int time_diff = abs((rtc_time.Hours - gps_data.hour) * 60 + (rtc_time.Minutes - gps_data.minute));

    // Sync RTC clock every time valid GPS data is obtained
    extern uint8_t is_first_boot;

    printf("GPS data valid, syncing RTC with GPS time\r\n");

    if (date_valid) {
        // GPS date valid, perform complete sync (GPS time + GPS date)
        if (GPS_SyncRTC(&gps_data)) {
            if (is_first_boot) {
                is_first_boot = 0; // 恢复：GPS同步成功后清零，后续使用120秒
                printf("First boot: RTC synchronized with GPS time and date: %02d:%02d:%02d %04d-%02d-%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       gps_data.year, gps_data.month, gps_data.day);
            } else {
                printf("RTC synchronized with GPS time and date: %02d:%02d:%02d %04d-%02d-%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       gps_data.year, gps_data.month, gps_data.day);
            }
            return HAL_OK;
        } else {
            printf("GPS full sync failed\r\n");
            return HAL_ERROR;
        }
    } else {
        // GPS date invalid but has positioning, use GPS time + keep current RTC date
        if (RTC_SetDateTime(gps_data.hour, gps_data.minute, gps_data.second,
                           rtc_date.Date, rtc_date.Month, rtc_date.Year) == HAL_OK) {
            if (is_first_boot) {
                is_first_boot = 0; // 恢复：GPS同步成功后清零，后续使用120秒
                printf("First boot: RTC synchronized with GPS time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       rtc_date.Date, rtc_date.Month, rtc_date.Year);
            } else {
                printf("RTC synchronized with GPS time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
                       gps_data.hour, gps_data.minute, gps_data.second,
                       rtc_date.Date, rtc_date.Month, rtc_date.Year);
            }
            return HAL_OK;
        } else {
            printf("GPS time sync failed\r\n");
            return HAL_ERROR;
        }
    }
}

// Sensor module initialization
HAL_StatusTypeDef SensorModule_Init(void)
{
//    printf("Sensor module initialization\r\n");

    // Initialize I2C
    __HAL_RCC_I2C1_CLK_ENABLE();
    MX_I2C1_Init();
    HAL_Delay(50);  // Reduced delay: 100ms -> 50ms

    return HAL_OK;
}

// Read sensor data
HAL_StatusTypeDef SensorModule_ReadData(void)
{
    // MCU temperature is already updated in PowerModule_ReadBatteryVoltage()
    // No need to update it again here as ADC_Value[] may contain stale data

    if (HAL_I2C_IsDeviceReady(&hi2c1, LSM6DS3_ADDR, 3, 100) == HAL_OK) {
        LSM6DS3_Init(&hi2c1);
        LSM6DS3_InitAttitude(&attitude);
        HAL_Delay(20);  // Reduced delay: 50ms -> 20ms
        LSM6DS3_ReadData(&hi2c1, &imuData);
        LSM6DS3_ComplementaryFilter(&imuData, &attitude);
        return HAL_OK;
    } else {
        memset(&imuData, 0, sizeof(LSM6DS3_Data));
        imuData.temp_celsius = 25.0f;
        memset(&attitude, 0, sizeof(LSM6DS3_Attitude));
        return HAL_ERROR;
    }
}

// GM20相关函数已全部移除

// Create data packet
HAL_StatusTypeDef DataModule_CreatePacket(char *output_buffer, uint16_t buffer_size)
{
    return Create_Data_String(output_buffer, buffer_size);
}

// Print data (替代GM20发送)
void DataModule_PrintData(const char *data_string)
{
    Print_Data_String(data_string);
}

// Print current time
void SystemModule_PrintCurrentTime(void)
{
    RTC_TimeTypeDef current_time;
    RTC_DateTypeDef current_date;
    HAL_RTC_GetTime(&hrtc, &current_time, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &current_date, RTC_FORMAT_BIN);
    printf("Current RTC time: %02d:%02d:%02d %02d/%02d/%02d\r\n",
           current_time.Hours, current_time.Minutes, current_time.Seconds,
           current_date.Date, current_date.Month, current_date.Year);
}

// GM20智能等待函数已移除

// System enter sleep mode
void SystemModule_EnterSleepMode(void)
{
    // 强制关闭LED，确保休眠前LED一定关闭
    Force_LED_Off();

    // Disable peripheral power
    PowerModule_DisablePeripherals();

    // Print time before sleep
    RTC_TimeTypeDef time_before_sleep;
    RTC_DateTypeDef date_before_sleep;
    HAL_RTC_GetTime(&hrtc, &time_before_sleep, RTC_FORMAT_BIN);
    HAL_RTC_GetDate(&hrtc, &date_before_sleep, RTC_FORMAT_BIN);
    printf("Time before sleep: %02d:%02d:%02d\r\n",
           time_before_sleep.Hours, time_before_sleep.Minutes, time_before_sleep.Seconds);

    // Use macro-defined sleep time
    printf("Sleep for %d seconds\r\n", SLEEP_DURATION_SECONDS);

    // Update sleep time configuration
    wakeup_counter = SLEEP_DURATION_SECONDS - 1;

    // Enter low power mode
    EnterStopMode();
}

// Battery voltage protection module implementation

// Check battery voltage and manage protection status
uint8_t BatteryProtection_CheckVoltage(void)
{
    extern float pw; // Current battery voltage

    if (low_voltage_protection_active) {
        // Currently in low voltage protection state, check if recovery is possible
        if (pw > BATTERY_RECOVERY_VOLTAGE_THRESHOLD) {
            // Voltage recovered, exit low voltage protection mode
            BatteryProtection_ExitLowVoltageMode();
            return 2; // Exit low voltage protection mode
        } else {
            // Voltage still too low, continue protection mode
            printf("Battery protection active: %.3fV < %.1fV\r\n",
                   pw, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
            return 1; // Continue low voltage protection mode
        }
    } else {
        // Currently in normal working state, check if need to enter protection mode
        if (pw < BATTERY_LOW_VOLTAGE_THRESHOLD) {
            // Voltage too low, enter low voltage protection mode
            BatteryProtection_EnterLowVoltageMode();
            return 1; // Enter low voltage protection mode
        } else {
            // Voltage normal, continue normal operation
            return 0; // Normal working mode
        }
    }
}

// Enter low voltage protection mode
void BatteryProtection_EnterLowVoltageMode(void)
{
    extern float pw; // Current battery voltage

    printf("*** LOW VOLTAGE PROTECTION ACTIVATED ***\r\n");
    printf("Battery voltage: %.3fV < %.1fV (threshold)\r\n",
           pw, BATTERY_LOW_VOLTAGE_THRESHOLD);
    printf("Shutting down RF module to save power\r\n");

    // Turn off RF module power to save power
    RF_PWR_OFF;

    // Set low voltage protection flag
    low_voltage_protection_active = 1;

    printf("Device entering low power protection mode\r\n");
}

// Exit low voltage protection mode
void BatteryProtection_ExitLowVoltageMode(void)
{
    extern float pw; // Current battery voltage

    printf("*** VOLTAGE RECOVERY DETECTED ***\r\n");
    printf("Battery voltage: %.3fV > %.1fV (recovery threshold)\r\n",
           pw, BATTERY_RECOVERY_VOLTAGE_THRESHOLD);
    printf("RF power control returned to main loop\r\n");

    // RF电源控制已移至主循环中

    // Clear low voltage protection flag
    low_voltage_protection_active = 0;

    printf("Device resuming normal operation\r\n");
}

// STM32内部温度传感器计算函数实现
/**
  * @brief  计算STM32内部温度传感器的温度值
  * @param  temp_adc_value: 温度传感器的ADC原始值
  * @retval 计算得到的温度值（摄氏度）
  */
float Calculate_MCU_Temperature(uint32_t temp_adc_value)
{
    // STM32L071工厂校准值地址 - 修正为130°C校准点
    uint16_t *temp30_cal = (uint16_t*)0x1FF8007A;  // 30°C时的校准值
    uint16_t *temp130_cal = (uint16_t*)0x1FF8007E; // 130°C时的校准值（修正）

    // 读取工厂校准值
    uint16_t cal_30 = *temp30_cal;
    uint16_t cal_130 = *temp130_cal;

    // 使用线性插值计算温度 - 修正为30°C到130°C范围
    // 公式: Temperature = 30 + (ADC_Value - CAL_30) * (130 - 30) / (CAL_130 - CAL_30)
    float temperature = 30.0f + ((float)temp_adc_value - (float)cal_30) * 100.0f / ((float)cal_130 - (float)cal_30);

    // 应用温度校准系数和偏移量
    #include "GPS.h"  // 包含温度校准宏定义
    temperature = temperature * TEMP_CALIBRATION_FACTOR + TEMP_OFFSET;

    return temperature;
}

// 数据缓存管理函数实现

// 初始化数据缓存
void DataCache_Init(void)
{
    memset(&data_cache, 0, sizeof(DataCache_t));
    memset(data_cache_buffer, 0, sizeof(data_cache_buffer));
    printf("Data cache initialized\r\n");
}

// 添加数据到缓存
HAL_StatusTypeDef DataCache_AddData(const char *data_string)
{
    if (data_cache.is_full) {
        return HAL_ERROR;  // 缓存已满
    }

    if (data_cache.count >= DATA_CACHE_SIZE) {
        data_cache.is_full = 1;
        return HAL_ERROR;
    }

    // 存储数据
    strncpy(data_cache.items[data_cache.write_index].data, data_string, DATA_CACHE_ITEM_SIZE - 1);
    data_cache.items[data_cache.write_index].data[DATA_CACHE_ITEM_SIZE - 1] = '\0';
    data_cache.items[data_cache.write_index].timestamp = device_wakeup_count;
    data_cache.items[data_cache.write_index].valid = 1;

    // 更新索引
    data_cache.write_index = (data_cache.write_index + 1) % DATA_CACHE_SIZE;
    data_cache.count++;

    return HAL_OK;
}

// 从缓存读取数据
HAL_StatusTypeDef DataCache_GetData(char *data_string)
{
    if (data_cache.count == 0) {
        return HAL_ERROR;  // 缓存为空
    }

    // 读取数据
    strncpy(data_string, data_cache.items[data_cache.read_index].data, DATA_CACHE_ITEM_SIZE);

    // 更新索引
    data_cache.read_index = (data_cache.read_index + 1) % DATA_CACHE_SIZE;
    data_cache.count--;

    return HAL_OK;
}

// 获取缓存中的数据条数
uint16_t DataCache_GetCount(void)
{
    return data_cache.count;
}

// 清空缓存
void DataCache_Clear(void)
{
    memset(&data_cache, 0, sizeof(DataCache_t));
    printf("Data cache cleared\r\n");
}

// 检查缓存是否已满
uint8_t DataCache_IsFull(void)
{
    return data_cache.is_full;
}

// 时间管理函数实现

// 获取当前累计唤醒时间（分钟）
uint32_t GetCurrentWakeupTime(void)
{
    return device_wakeup_count * SLEEP_DURATION_SECONDS / 60;
}

// GM20工作时间计算函数已移除

// GM20单条数据发送函数已移除

// GM20智能等待函数已移除

// LED强制关闭函数实现
void Force_LED_Off(void)
{
    // 强制关闭LED，多次调用确保LED关闭
    LED1_OFF;
    HAL_Delay(5);   // 短暂延时
    LED1_OFF;       // 再次关闭
    HAL_Delay(5);   // 再次延时
    LED1_OFF;       // 第三次关闭，确保LED关闭
}

/* USER CODE END Application */

